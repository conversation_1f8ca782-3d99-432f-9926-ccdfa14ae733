"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/ai/conversation/start";
exports.ids = ["pages/api/ai/conversation/start"];
exports.modules = {

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/google":
/*!*********************************************!*\
  !*** external "next-auth/providers/google" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/google");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fconversation%2Fstart&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai%5Cconversation%5Cstart.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fconversation%2Fstart&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai%5Cconversation%5Cstart.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_ai_conversation_start_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\ai\\conversation\\start.ts */ \"(api)/./src/pages/api/ai/conversation/start.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_ai_conversation_start_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_ai_conversation_start_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/ai/conversation/start\",\n        pathname: \"/api/ai/conversation/start\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_ai_conversation_start_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fconversation%2Fstart&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai%5Cconversation%5Cstart.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/ai/conversation/start.ts":
/*!************************************************!*\
  !*** ./src/pages/api/ai/conversation/start.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../auth/[...nextauth] */ \"(api)/./src/pages/api/auth/[...nextauth].ts\");\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            success: false,\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // Get user session\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n        if (!session?.user?.id) {\n            return res.status(401).json({\n                success: false,\n                message: \"Unauthorized\"\n            });\n        }\n        const { userRole, language, sessionType, culturalContext } = req.body;\n        // Validate required fields\n        if (!userRole || !language) {\n            return res.status(400).json({\n                success: false,\n                message: \"Missing required fields: userRole, language\"\n            });\n        }\n        // Forward request to the main API with proper authentication\n        const apiResponse = await fetch(`${process.env.API_BASE_URL || \"http://localhost:3001\"}/api/v1/ai/v2/conversation/start`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": `Bearer ${session.user.id}`,\n                \"X-User-ID\": session.user.id\n            },\n            body: JSON.stringify({\n                userRole,\n                language,\n                sessionType: sessionType || \"onboarding\",\n                culturalContext: culturalContext || {\n                    location: \"سوريا\",\n                    dialect: \"general\"\n                },\n                userData: req.body.userData || {}\n            })\n        });\n        if (!apiResponse.ok) {\n            const errorData = await apiResponse.json().catch(()=>({}));\n            throw new Error(errorData.message || `API request failed with status ${apiResponse.status}`);\n        }\n        const apiData = await apiResponse.json();\n        if (!apiData.success) {\n            throw new Error(apiData.message || \"Failed to start conversation\");\n        }\n        // Generate welcome message based on role and language\n        const welcomeMessage = generateWelcomeMessage(userRole, language);\n        // Return successful response with proper structure\n        return res.status(201).json({\n            success: true,\n            message: \"AI conversation started successfully\",\n            data: {\n                id: apiData.data.sessionId,\n                sessionId: apiData.data.sessionId,\n                currentStep: apiData.data.currentStep || \"welcome\",\n                userRole: userRole,\n                messages: [\n                    {\n                        id: `welcome_${Date.now()}`,\n                        role: \"assistant\",\n                        content: apiData.data.welcomeMessage || welcomeMessage,\n                        timestamp: new Date().toISOString(),\n                        confidence: 1.0\n                    }\n                ],\n                extractedData: apiData.data.extractedData || {},\n                status: apiData.data.status || \"active\",\n                completionRate: 0.0\n            }\n        });\n    } catch (error) {\n        console.error(\"Error starting AI conversation:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"Internal server error\",\n            error: error.message\n        });\n    }\n}\n/**\n * Generate welcome message based on user role and language\n */ function generateWelcomeMessage(userRole, language) {\n    if (language === \"ar\") {\n        if (userRole === \"EXPERT\") {\n            return `مرحباً بك! أنا مساعدك الذكي في منصة فريلا سوريا. \n\nسأساعدك في إعداد ملفك المهني بطريقة تفاعلية وذكية. سنتحدث عن:\n• مهاراتك وخبراتك المهنية\n• الخدمات التي تريد تقديمها\n• أسعارك المناسبة للسوق السوري\n• كيفية جذب العملاء المناسبين\n\nلنبدأ! ما هو مجال تخصصك الرئيسي؟`;\n        } else {\n            return `مرحباً بك! أنا مساعدك الذكي في منصة فريلا سوريا.\n\nسأساعدك في تحديد احتياجاتك وإيجاد أفضل الخبراء لمشاريعك. سنتحدث عن:\n• نوع المشاريع التي تحتاج إنجازها\n• الميزانية والجدول الزمني المناسب\n• المهارات المطلوبة في الخبراء\n• كيفية التواصل الفعال مع الخبراء\n\nلنبدأ! ما نوع المشروع الذي تفكر في تنفيذه؟`;\n        }\n    } else {\n        if (userRole === \"EXPERT\") {\n            return `Welcome! I'm your AI assistant on Freela Syria platform.\n\nI'll help you set up your professional profile interactively. We'll discuss:\n• Your skills and professional experience\n• Services you want to offer\n• Appropriate pricing for the Syrian market\n• How to attract the right clients\n\nLet's start! What is your main area of expertise?`;\n        } else {\n            return `Welcome! I'm your AI assistant on Freela Syria platform.\n\nI'll help you identify your needs and find the best experts for your projects. We'll discuss:\n• Types of projects you need to complete\n• Appropriate budget and timeline\n• Required skills in experts\n• How to communicate effectively with experts\n\nLet's start! What type of project are you thinking of implementing?`;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/ai/conversation/start.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].ts":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"next-auth/providers/google\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// import { PrismaAdapter } from '@next-auth/prisma-adapter';\n// import { prisma } from '@freela/database';\n// import { Prisma } from '@prisma/client';\nconst authOptions = {\n    // adapter: PrismaAdapter(prisma), // Temporarily disabled for testing\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: process.env.GOOGLE_CLIENT_ID || \"\",\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET || \"\",\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            try {\n                console.log(\"\\uD83D\\uDD10 SignIn callback triggered:\", {\n                    user: user.email,\n                    provider: account?.provider,\n                    profile: profile?.name\n                });\n                if (account?.provider === \"google\") {\n                    console.log(\"✅ Google OAuth sign-in successful for:\", user.email);\n                    // For now, we'll skip database operations and just allow sign-in\n                    // TODO: Re-enable database operations once connection is fixed\n                    return true;\n                }\n                return true;\n            } catch (error) {\n                console.error(\"❌ Error during sign in:\", error);\n                return false;\n            }\n        },\n        async jwt ({ token, user, trigger, session }) {\n            console.log(\"\\uD83D\\uDD11 JWT callback triggered:\", {\n                hasUser: !!user,\n                tokenEmail: token.email,\n                userEmail: user?.email,\n                trigger\n            });\n            if (user) {\n                console.log(\"\\uD83D\\uDC64 Setting up JWT token for new user:\", user.email);\n                // For testing, we'll set default values without database lookup\n                token.id = user.id || user.email || \"temp-id\"; // Use email as fallback ID\n                token.role = undefined; // CRITICAL FIX: No default role - user must select during onboarding\n                token.status = \"ACTIVE\";\n                token.language = \"ar\";\n                token.firstName = user.name?.split(\" \")[0] || \"User\";\n                token.lastName = user.name?.split(\" \").slice(1).join(\" \") || \"\";\n                token.avatar = user.image || null;\n                token.hasCompletedOnboarding = false; // Always false for new users\n                console.log(\"✅ JWT token configured:\", {\n                    id: token.id,\n                    role: token.role,\n                    hasCompletedOnboarding: token.hasCompletedOnboarding\n                });\n            }\n            // Handle onboarding completion updates\n            if (trigger === \"update\" && session) {\n                console.log(\"\\uD83D\\uDD04 JWT token update triggered with data:\", session);\n                // Update role if provided\n                if (session.role) {\n                    token.role = session.role;\n                    console.log(\"✅ Role updated in token:\", session.role);\n                }\n                // Update onboarding completion status if provided\n                if (session.hasCompletedOnboarding !== undefined) {\n                    token.hasCompletedOnboarding = session.hasCompletedOnboarding;\n                    console.log(\"✅ Onboarding status updated in token:\", session.hasCompletedOnboarding);\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            console.log(\"\\uD83D\\uDCCB Session callback triggered:\", {\n                tokenEmail: token.email,\n                sessionEmail: session.user?.email,\n                hasCompletedOnboarding: token.hasCompletedOnboarding\n            });\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.status = token.status;\n                session.user.language = token.language;\n                session.user.firstName = token.firstName;\n                session.user.lastName = token.lastName;\n                session.user.avatar = token.avatar;\n                session.user.hasCompletedOnboarding = token.hasCompletedOnboarding;\n                console.log(\"✅ Session configured:\", {\n                    id: session.user.id,\n                    role: session.user.role,\n                    hasCompletedOnboarding: session.user.hasCompletedOnboarding\n                });\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Handle role-based redirects after successful authentication\n            try {\n                console.log(\"\\uD83D\\uDD04 NextAuth redirect called with:\", {\n                    url,\n                    baseUrl\n                });\n                // Check if this is a post-authentication callback\n                if (url.includes(\"/api/auth/callback\") || url === baseUrl || url === `${baseUrl}/`) {\n                    console.log(\"\\uD83D\\uDD04 Post-authentication callback detected\");\n                    // CRITICAL FIX: Always redirect to AI onboarding first for proper flow\n                    // The AI onboarding page will handle the logic for completed users\n                    console.log(\"\\uD83E\\uDD16 Redirecting ALL authenticated users to AI onboarding for proper flow handling\");\n                    return `${baseUrl}/ai-onboarding`;\n                }\n                // Handle relative URLs\n                if (url.startsWith(\"/\")) {\n                    const fullUrl = `${baseUrl}${url}`;\n                    console.log(\"\\uD83D\\uDD04 Converting relative URL to absolute:\", fullUrl);\n                    return fullUrl;\n                }\n                // Handle same-origin URLs\n                if (url.startsWith(baseUrl)) {\n                    console.log(\"\\uD83D\\uDD04 Same-origin URL redirect:\", url);\n                    return url;\n                }\n                // Default fallback - redirect to landing page for safety\n                console.log(\"\\uD83C\\uDFE0 Fallback redirect to landing page\");\n                return `${baseUrl}/?auth=success`;\n            } catch (error) {\n                console.error(\"❌ Redirect error:\", error);\n                return `${baseUrl}/?auth=error`;\n            }\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    events: {\n        async signIn ({ user, account }) {\n            console.log(`🎉 User ${user.email} signed in with ${account?.provider}`);\n        // TODO: Re-enable database operations once connection is fixed\n        },\n        async signOut ({ token }) {\n            console.log(`👋 User ${token?.email} signed out`);\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fconversation%2Fstart&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai%5Cconversation%5Cstart.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();