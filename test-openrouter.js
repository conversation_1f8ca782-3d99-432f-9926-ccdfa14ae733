const fetch = require('node-fetch');

async function testOpenRouter() {
  try {
    console.log('Testing OpenRouter API...');
    
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer 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',
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://freela-syria.com',
        'X-Title': 'Freela Syria Test'
      },
      body: JSON.stringify({
        model: 'openai/gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: 'Hello, this is a test message. Please respond in Arabic.'
          }
        ],
        max_tokens: 100
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ OpenRouter API is working!');
    console.log('Response:', data.choices[0].message.content);
    
  } catch (error) {
    console.error('❌ OpenRouter API Error:', error.message);
  }
}

testOpenRouter();
