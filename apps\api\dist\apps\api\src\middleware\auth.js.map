{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AACA,wCAAkE;AAClE,4CAA2D;AAC3D,+CAA6C;AAqB7C;;GAEG;AACI,MAAM,YAAY,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACpF,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAW,CAAC;QACxD,MAAM,KAAK,GAAG,gBAAS,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAE3D,iFAAiF;QACjF,IAAI,CAAC,KAAK,IAAI,YAAY,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACH,+DAA+D;gBAC/D,MAAM,IAAI,GAAG,MAAM,oBAAS,CAAC,YAAY,CAAC,YAAY,EAAE;oBACtD,EAAE,EAAE,IAAI;oBACR,KAAK,EAAE,IAAI;oBACX,SAAS,EAAE,IAAI;oBACf,QAAQ,EAAE,IAAI;oBACd,IAAI,EAAE,IAAI;oBACV,MAAM,EAAE,IAAI;oBACZ,aAAa,EAAE,IAAI;oBACnB,aAAa,EAAE,IAAI;iBACpB,CAAC,CAAC;gBAEH,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;oBACrC,GAAG,CAAC,IAAI,GAAG;wBACT,GAAG,IAAI;wBACP,SAAS,EAAE,gBAAgB,YAAY,EAAE;qBAC1C,CAAC;oBACF,OAAO,IAAI,EAAE,CAAC;gBAChB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;QACL,CAAC;QAED,eAAe;QACf,MAAM,OAAO,GAAG,eAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,IAAA,yBAAgB,EAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,GAAG,CAAC,CAAC;YAC1F,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,iCAAiC;gBAC1C,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;QACL,CAAC;QAED,mBAAmB;QACnB,MAAM,cAAc,GAAG,MAAM,mBAAY,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,IAAA,yBAAgB,EAAC,yBAAyB,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;YAC3G,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE,iBAAiB;aACxB,CAAC,CAAC;QACL,CAAC;QAED,+BAA+B;QAC/B,MAAM,IAAI,GAAG,MAAM,oBAAS,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE;YACxD,EAAE,EAAE,IAAI;YACR,KAAK,EAAE,IAAI;YACX,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,IAAI;YACd,IAAI,EAAE,IAAI;YACV,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI;YACnB,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAA,yBAAgB,EAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;YACpE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE,gBAAgB;aACvB,CAAC,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAA,yBAAgB,EAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;YACzF,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE,kBAAkB;aACzB,CAAC,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,MAAM,mBAAY,CAAC,qBAAqB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE5D,yBAAyB;QACzB,GAAG,CAAC,IAAI,GAAG;YACT,GAAG,IAAI;YACP,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAC3D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,uBAAuB;YAChC,IAAI,EAAE,YAAY;SACnB,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAhHW,QAAA,YAAY,gBAgHvB;AAEF;;GAEG;AACI,MAAM,oBAAoB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC5F,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,MAAM,KAAK,GAAG,gBAAS,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAE3D,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,yDAAyD;QACzD,MAAM,OAAO,GAAG,eAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAClD,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,cAAc,GAAG,MAAM,mBAAY,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC7E,IAAI,cAAc,EAAE,CAAC;gBACnB,MAAM,IAAI,GAAG,MAAM,oBAAS,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE;oBACxD,EAAE,EAAE,IAAI;oBACR,KAAK,EAAE,IAAI;oBACX,SAAS,EAAE,IAAI;oBACf,QAAQ,EAAE,IAAI;oBACd,IAAI,EAAE,IAAI;oBACV,MAAM,EAAE,IAAI;oBACZ,aAAa,EAAE,IAAI;oBACnB,aAAa,EAAE,IAAI;iBACpB,CAAC,CAAC;gBAEH,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;oBACrC,GAAG,CAAC,IAAI,GAAG;wBACT,GAAG,IAAI;wBACP,SAAS,EAAE,OAAO,CAAC,SAAS;qBAC7B,CAAC;oBACF,MAAM,mBAAY,CAAC,qBAAqB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QACpE,IAAI,EAAE,CAAC,CAAC,kCAAkC;IAC5C,CAAC;AACH,CAAC,CAAC;AAxCW,QAAA,oBAAoB,wBAwC/B;AAEF;;GAEG;AACI,MAAM,SAAS,GAAG,CAAC,GAAG,YAAsB,EAAE,EAAE;IACrD,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,IAAA,yBAAgB,EAAC,6BAA6B,EAAE;gBAC9C,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;gBACnB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI;gBACvB,aAAa,EAAE,YAAY;gBAC3B,QAAQ,EAAE,GAAG,CAAC,IAAI;aACnB,EAAE,GAAG,CAAC,CAAC;YAER,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE,0BAA0B;aACjC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AA3BW,QAAA,SAAS,aA2BpB;AAEF;;GAEG;AACI,MAAM,wBAAwB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC1F,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,yBAAyB;YAClC,IAAI,EAAE,eAAe;SACtB,CAAC,CAAC;IACL,CAAC;IAED,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAC5B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,6BAA6B;YACtC,IAAI,EAAE,6BAA6B;SACpC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAlBW,QAAA,wBAAwB,4BAkBnC;AAEF;;GAEG;AACI,MAAM,wBAAwB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC1F,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,yBAAyB;YAClC,IAAI,EAAE,eAAe;SACtB,CAAC,CAAC;IACL,CAAC;IAED,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAC5B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,6BAA6B;YACtC,IAAI,EAAE,6BAA6B;SACpC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAlBW,QAAA,wBAAwB,4BAkBnC;AAEF;;GAEG;AACI,MAAM,gBAAgB,GAAG,CAAC,kBAA0B,IAAI,EAAE,cAAsB,QAAQ,EAAE,EAAE;IACjG,OAAO,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAC/D,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QAC/C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE,sBAAsB;aAC7B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC;YACH,sFAAsF;YACtF,8CAA8C;YAC9C,IAAI,WAAW,KAAK,QAAQ,IAAI,UAAU,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC3D,IAAA,yBAAgB,EAAC,8BAA8B,EAAE;oBAC/C,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;oBACnB,UAAU;oBACV,YAAY,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI;iBAC1C,EAAE,GAAG,CAAC,CAAC;gBAER,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,gCAAgC;oBACzC,IAAI,EAAE,eAAe;iBACtB,CAAC,CAAC;YACL,CAAC;YAED,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAClF,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,qCAAqC;gBAC9C,IAAI,EAAE,uBAAuB;aAC9B,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AA9CW,QAAA,gBAAgB,oBA8C3B;AAEF;;GAEG;AACU,QAAA,SAAS,GAAG,IAAA,iBAAS,EAAC,OAAO,CAAC,CAAC;AAE5C;;GAEG;AACU,QAAA,UAAU,GAAG,IAAA,iBAAS,EAAC,QAAQ,CAAC,CAAC;AAE9C;;GAEG;AACU,QAAA,UAAU,GAAG,IAAA,iBAAS,EAAC,QAAQ,CAAC,CAAC;AAE9C;;GAEG;AACU,QAAA,aAAa,GAAG,IAAA,iBAAS,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAE1D;;GAEG;AACU,QAAA,aAAa,GAAG,IAAA,iBAAS,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAE1D;;GAEG;AACU,QAAA,OAAO,GAAG,IAAA,iBAAS,EAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC"}