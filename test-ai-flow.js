const fetch = require('node-fetch');

const API_BASE = 'http://localhost:3001/api/v1';
const LANDING_API_BASE = 'http://localhost:3004/api';

async function testCompleteAIFlow() {
  console.log('🧪 Testing Complete AI Chatbot Flow...\n');

  try {
    // Step 1: Test API server health
    console.log('1️⃣ Testing API server health...');
    const healthResponse = await fetch(`${API_BASE.replace('/api/v1', '')}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ API Health:', healthData.status);

    // Step 2: Test AI services debug
    console.log('\n2️⃣ Testing AI services debug...');
    const debugResponse = await fetch(`${API_BASE}/ai/debug/services`);
    const debugData = await debugResponse.json();
    console.log('✅ AI Services:', debugData.data);

    // Step 3: Test direct AI conversation start (Phase 3)
    console.log('\n3️⃣ Testing direct AI conversation start...');
    const conversationResponse = await fetch(`${API_BASE}/ai/v2/conversation/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': 'test-user-123'
      },
      body: JSON.stringify({
        userRole: 'EXPERT',
        language: 'ar',
        sessionType: 'onboarding',
        culturalContext: {
          location: 'سوريا',
          dialect: 'general'
        }
      })
    });

    if (!conversationResponse.ok) {
      const errorText = await conversationResponse.text();
      throw new Error(`Conversation start failed: ${conversationResponse.status} - ${errorText}`);
    }

    const conversationData = await conversationResponse.json();
    console.log('✅ Conversation started:', {
      sessionId: conversationData.data.sessionId,
      welcomeMessage: conversationData.data.welcomeMessage?.substring(0, 100) + '...'
    });

    // Step 4: Test sending a message
    console.log('\n4️⃣ Testing message sending...');
    const messageResponse = await fetch(`${API_BASE}/ai/v2/conversation/${conversationData.data.sessionId}/message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': 'test-user-123'
      },
      body: JSON.stringify({
        message: 'مرحبا، أنا مطور ويب متخصص في React و Node.js',
        messageType: 'text'
      })
    });

    if (!messageResponse.ok) {
      const errorText = await messageResponse.text();
      throw new Error(`Message send failed: ${messageResponse.status} - ${errorText}`);
    }

    const messageData = await messageResponse.json();
    console.log('✅ Message sent and AI responded:', {
      aiResponse: messageData.data.aiResponse?.substring(0, 100) + '...',
      nextStep: messageData.data.nextStep
    });

    // Step 5: Test landing page API proxy
    console.log('\n5️⃣ Testing landing page API proxy...');
    const proxyResponse = await fetch(`${LANDING_API_BASE}/ai/conversation/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'next-auth.session-token=test-session'
      },
      body: JSON.stringify({
        userRole: 'CLIENT',
        language: 'ar',
        sessionType: 'onboarding',
        culturalContext: {
          location: 'دمشق',
          dialect: 'general'
        }
      })
    });

    if (proxyResponse.ok) {
      const proxyData = await proxyResponse.json();
      console.log('✅ Landing page proxy working:', proxyData.success);
    } else {
      console.log('⚠️ Landing page proxy needs authentication (expected)');
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('- API server is running ✅');
    console.log('- AI services are loaded ✅');
    console.log('- OpenRouter integration working ✅');
    console.log('- Conversation flow working ✅');
    console.log('- Message exchange working ✅');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testCompleteAIFlow();
