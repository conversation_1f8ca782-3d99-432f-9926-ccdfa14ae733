import { Request, Response, NextFunction } from 'express';
import { jwtUtils, authUtils, sessionUtils } from '../utils/auth';
import { logger, logSecurityEvent } from '../utils/logger';
import { dbService } from '@freela/database';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: string;
        sessionId: string;
        firstName: string;
        lastName: string;
        status: string;
        emailVerified: boolean;
        phoneVerified: boolean;
      };
    }
  }
}

/**
 * Authentication middleware - verifies JWT token and loads user data
 */
export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const userIdHeader = req.headers['x-user-id'] as string;
    const token = authUtils.extractTokenFromHeader(authHeader);

    // For development/testing: allow direct user ID authentication from landing page
    if (!token && userIdHeader) {
      try {
        // Load user data directly by ID (for landing page integration)
        const user = await dbService.findUserById(userIdHeader, {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          status: true,
          emailVerified: true,
          phoneVerified: true,
        });

        if (user && user.status === 'ACTIVE') {
          req.user = {
            ...user,
            sessionId: `temp_session_${userIdHeader}`,
          };
          return next();
        }
      } catch (error) {
        logger.warn('Direct user ID authentication failed', { error, userIdHeader });
      }
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token is required',
        code: 'TOKEN_MISSING',
      });
    }

    // Verify token
    const payload = jwtUtils.verifyAccessToken(token);
    if (!payload) {
      logSecurityEvent('invalid_token_attempt', { token: token.substring(0, 20) + '...' }, req);
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired access token',
        code: 'TOKEN_INVALID',
      });
    }

    // Validate session
    const isSessionValid = await sessionUtils.validateSession(payload.sessionId);
    if (!isSessionValid) {
      logSecurityEvent('invalid_session_attempt', { sessionId: payload.sessionId, userId: payload.userId }, req);
      return res.status(401).json({
        success: false,
        message: 'Session has expired',
        code: 'SESSION_EXPIRED',
      });
    }

    // Load user data from database
    const user = await dbService.findUserById(payload.userId, {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      status: true,
      emailVerified: true,
      phoneVerified: true,
    });

    if (!user) {
      logSecurityEvent('user_not_found', { userId: payload.userId }, req);
      return res.status(401).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND',
      });
    }

    // Check if user is active
    if (user.status !== 'ACTIVE') {
      logSecurityEvent('inactive_user_attempt', { userId: user.id, status: user.status }, req);
      return res.status(403).json({
        success: false,
        message: 'Account is not active',
        code: 'ACCOUNT_INACTIVE',
      });
    }

    // Update session activity
    await sessionUtils.updateSessionActivity(payload.sessionId);

    // Attach user to request
    req.user = {
      ...user,
      sessionId: payload.sessionId,
    };

    next();
  } catch (error) {
    logger.error('Authentication middleware error', { error });
    return res.status(500).json({
      success: false,
      message: 'Authentication failed',
      code: 'AUTH_ERROR',
    });
  }
};

/**
 * Optional authentication middleware - doesn't fail if no token provided
 */
export const optionalAuthenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authUtils.extractTokenFromHeader(authHeader);
    
    if (!token) {
      return next();
    }
    
    // Try to authenticate, but don't fail if it doesn't work
    const payload = jwtUtils.verifyAccessToken(token);
    if (payload) {
      const isSessionValid = await sessionUtils.validateSession(payload.sessionId);
      if (isSessionValid) {
        const user = await dbService.findUserById(payload.userId, {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          status: true,
          emailVerified: true,
          phoneVerified: true,
        });
        
        if (user && user.status === 'ACTIVE') {
          req.user = {
            ...user,
            sessionId: payload.sessionId,
          };
          await sessionUtils.updateSessionActivity(payload.sessionId);
        }
      }
    }
    
    next();
  } catch (error) {
    logger.error('Optional authentication middleware error', { error });
    next(); // Continue without authentication
  }
};

/**
 * Role-based authorization middleware
 */
export const authorize = (...allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED',
      });
    }
    
    if (!allowedRoles.includes(req.user.role)) {
      logSecurityEvent('unauthorized_access_attempt', {
        userId: req.user.id,
        userRole: req.user.role,
        requiredRoles: allowedRoles,
        endpoint: req.path,
      }, req);
      
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
      });
    }
    
    next();
  };
};

/**
 * Email verification requirement middleware
 */
export const requireEmailVerification = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required',
      code: 'AUTH_REQUIRED',
    });
  }
  
  if (!req.user.emailVerified) {
    return res.status(403).json({
      success: false,
      message: 'Email verification required',
      code: 'EMAIL_VERIFICATION_REQUIRED',
    });
  }
  
  next();
};

/**
 * Phone verification requirement middleware
 */
export const requirePhoneVerification = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required',
      code: 'AUTH_REQUIRED',
    });
  }
  
  if (!req.user.phoneVerified) {
    return res.status(403).json({
      success: false,
      message: 'Phone verification required',
      code: 'PHONE_VERIFICATION_REQUIRED',
    });
  }
  
  next();
};

/**
 * Resource ownership middleware - checks if user owns the resource
 */
export const requireOwnership = (resourceIdParam: string = 'id', userIdField: string = 'userId') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED',
      });
    }
    
    const resourceId = req.params[resourceIdParam];
    if (!resourceId) {
      return res.status(400).json({
        success: false,
        message: 'Resource ID is required',
        code: 'RESOURCE_ID_REQUIRED',
      });
    }
    
    try {
      // This is a generic implementation - you might need to customize based on your models
      // For now, we'll check if the user ID matches
      if (userIdField === 'userId' && resourceId !== req.user.id) {
        logSecurityEvent('unauthorized_resource_access', {
          userId: req.user.id,
          resourceId,
          resourceType: req.route?.path || req.path,
        }, req);
        
        return res.status(403).json({
          success: false,
          message: 'Access denied to this resource',
          code: 'ACCESS_DENIED',
        });
      }
      
      next();
    } catch (error) {
      logger.error('Ownership check error', { error, resourceId, userId: req.user.id });
      return res.status(500).json({
        success: false,
        message: 'Failed to verify resource ownership',
        code: 'OWNERSHIP_CHECK_ERROR',
      });
    }
  };
};

/**
 * Admin only middleware
 */
export const adminOnly = authorize('ADMIN');

/**
 * Expert only middleware
 */
export const expertOnly = authorize('EXPERT');

/**
 * Client only middleware
 */
export const clientOnly = authorize('CLIENT');

/**
 * Expert or Admin middleware
 */
export const expertOrAdmin = authorize('EXPERT', 'ADMIN');

/**
 * Client or Admin middleware
 */
export const clientOrAdmin = authorize('CLIENT', 'ADMIN');

/**
 * Any authenticated user middleware
 */
export const anyUser = authorize('CLIENT', 'EXPERT', 'ADMIN');
