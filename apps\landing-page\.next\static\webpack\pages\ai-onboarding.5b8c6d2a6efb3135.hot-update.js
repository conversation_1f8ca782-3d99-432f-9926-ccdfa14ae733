"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ai-onboarding",{

/***/ "./src/components/ai-onboarding/ChatInterface.tsx":
/*!********************************************************!*\
  !*** ./src/components/ai-onboarding/ChatInterface.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\nconst MessageBubble = (param)=>{\n    let { message, isLast } = param;\n    const isUser = message.role === \"user\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            scale: 1\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: \"flex \".concat(isUser ? \"justify-end\" : \"justify-start\", \" mb-4\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-end space-x-2 rtl:space-x-reverse max-w-xs lg:max-w-md\",\n            children: [\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\\n            px-4 py-3 rounded-2xl shadow-sm backdrop-blur-sm\\n            \".concat(isUser ? \"bg-primary-500 text-white rounded-br-md\" : \"bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-md border border-gray-200 dark:border-gray-600\", \"\\n          \"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm leading-relaxed whitespace-pre-wrap\",\n                            children: message.content\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs mt-2 \".concat(isUser ? \"text-primary-100\" : \"text-gray-500 dark:text-gray-400\"),\n                            children: new Date(message.timestamp).toLocaleTimeString(\"ar-SA\", {\n                                hour: \"2-digit\",\n                                minute: \"2-digit\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, undefined),\n                isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MessageBubble;\nconst TypingIndicator = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 10\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -10\n        },\n        className: \"flex justify-start mb-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-end space-x-2 rtl:space-x-reverse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-700 px-4 py-3 rounded-2xl rounded-bl-md shadow-sm border border-gray-200 dark:border-gray-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"0.1s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"0.2s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TypingIndicator;\nconst ChatInterface = (param)=>{\n    let { session, onComplete, isLoading } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(session.messages || []);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSending, setIsSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        isTyping\n    ]);\n    // Focus input on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _inputRef_current;\n        (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n    }, []);\n    /**\n   * Send message to AI\n   */ const sendMessage = async ()=>{\n        if (!inputValue.trim() || isSending) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            role: \"user\",\n            content: inputValue.trim(),\n            timestamp: new Date().toISOString()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue(\"\");\n        setIsSending(true);\n        setIsTyping(true);\n        try {\n            console.log(\"Sending message to AI:\", {\n                sessionId: session.id,\n                message: inputValue.trim(),\n                messageType: \"text\"\n            });\n            const response = await fetch(\"/api/ai/conversation/message\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    sessionId: session.id,\n                    message: inputValue.trim(),\n                    messageType: \"text\"\n                })\n            });\n            console.log(\"Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"API Error:\", errorText);\n                throw new Error(\"Failed to send message: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log(\"API Response:\", data);\n            if (data.success) {\n                const aiMessage = {\n                    id: data.data.aiMessage.id || \"ai_\".concat(Date.now()),\n                    role: \"assistant\",\n                    content: data.data.aiMessage.content || \"شكراً لك على رسالتك.\",\n                    timestamp: data.data.aiMessage.timestamp || new Date().toISOString(),\n                    confidence: data.data.aiMessage.confidence,\n                    extractedData: data.data.aiMessage.extractedData\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiMessage\n                    ]);\n                // Check if conversation is completed\n                if (data.data.isCompleted) {\n                    setTimeout(()=>{\n                        onComplete({\n                            ...session,\n                            messages: [\n                                ...messages,\n                                userMessage,\n                                aiMessage\n                            ],\n                            extractedData: data.data.extractedData,\n                            completionRate: 1.0\n                        });\n                    }, 1000);\n                }\n            } else {\n                throw new Error(data.message || \"Failed to get AI response\");\n            }\n        } catch (error) {\n            console.error(\"Failed to send message:\", error);\n            // Add error message\n            const errorMessage = {\n                id: \"error_\".concat(Date.now()),\n                role: \"assistant\",\n                content: \"عذراً، حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.\",\n                timestamp: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsTyping(false);\n            setIsSending(false);\n        }\n    };\n    /**\n   * Handle Enter key press\n   */ const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto h-[600px] flex flex-col bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-primary-500 to-primary-600 text-white p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-white/20 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold\",\n                                            children: session.userRole === \"EXPERT\" ? \"مساعد الخبراء الذكي\" : \"مساعد العملاء الذكي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-primary-100\",\n                                            children: \"متصل الآن\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-primary-100\",\n                                    children: \"التقدم\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-bold\",\n                                    children: [\n                                        Math.round(session.completionRate * 100),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900\",\n                children: [\n                    messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageBubble, {\n                            message: message,\n                            isLast: index === messages.length - 1\n                        }, message.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                        children: isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypingIndicator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 24\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-end space-x-3 rtl:space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputValue,\n                                onChange: (e)=>setInputValue(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"اكتب رسالتك هنا...\",\n                                className: \" w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none max-h-32 placeholder-gray-500 dark:placeholder-gray-400 \",\n                                rows: 1,\n                                disabled: isSending || isLoading\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: sendMessage,\n                            disabled: !inputValue.trim() || isSending || isLoading,\n                            className: \" p-3 bg-primary-500 text-white rounded-xl hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex-shrink-0 \",\n                            children: isSending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\ChatInterface.tsx\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInterface, \"v73GfLbFat+W0Fm0VhTxAsmbWWU=\");\n_c2 = ChatInterface;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ChatInterface);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MessageBubble\");\n$RefreshReg$(_c1, \"TypingIndicator\");\n$RefreshReg$(_c2, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ai-onboarding/ChatInterface.tsx\n"));

/***/ })

});