"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ai-onboarding",{

/***/ "./src/pages/ai-onboarding.tsx":
/*!*************************************!*\
  !*** ./src/pages/ai-onboarding.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"../../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_seo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-seo */ \"../../node_modules/next-seo/lib/next-seo.module.js\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout/index.tsx\");\n/* harmony import */ var _components_ai_onboarding_RoleSelection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ai-onboarding/RoleSelection */ \"./src/components/ai-onboarding/RoleSelection.tsx\");\n/* harmony import */ var _components_ai_onboarding_DataCollectionForm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ai-onboarding/DataCollectionForm */ \"./src/components/ai-onboarding/DataCollectionForm.tsx\");\n/* harmony import */ var _components_ai_onboarding_AIIntroduction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ai-onboarding/AIIntroduction */ \"./src/components/ai-onboarding/AIIntroduction.tsx\");\n/* harmony import */ var _components_ai_onboarding_ChatInterface__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ai-onboarding/ChatInterface */ \"./src/components/ai-onboarding/ChatInterface.tsx\");\n/* harmony import */ var _components_ai_onboarding_OnboardingProgress__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ai-onboarding/OnboardingProgress */ \"./src/components/ai-onboarding/OnboardingProgress.tsx\");\n/* harmony import */ var _components_ai_onboarding_CompletionCelebration__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ai-onboarding/CompletionCelebration */ \"./src/components/ai-onboarding/CompletionCelebration.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// AI Onboarding Components\n\n\n\n\n\n\nconst AIOnboardingPage = ()=>{\n    var _session_user, _session_user_name, _session_user1, _session_user_name1, _session_user2, _session_user3;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)([\n        \"common\",\n        \"ai-onboarding\"\n    ]);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { data: session, status, update } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const { role: urlRole } = router.query;\n    // State\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"role_selection\");\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [collectedData, setCollectedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [aiSession, setAiSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize role from URL or session with optimized redirect handling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Prevent flash content by handling redirects immediately\n        if (status === \"loading\") {\n            return; // Still loading, wait\n        }\n        if (status === \"unauthenticated\") {\n            // Immediate redirect for unauthenticated users\n            router.replace(\"/?auth=required\");\n            return;\n        }\n        if (!(session === null || session === void 0 ? void 0 : session.user)) {\n            // Immediate redirect if no user session\n            router.replace(\"/?auth=session-error\");\n            return;\n        }\n        // CRITICAL FIX: Handle completed onboarding users immediately to prevent flash\n        if (session.user.hasCompletedOnboarding) {\n            setIsRedirecting(true);\n            // Use window.location for immediate redirect without flash\n            const userRole = session.user.role;\n            switch(userRole){\n                case \"ADMIN\":\n                    window.location.replace(\"http://localhost:3001/dashboard\");\n                    break;\n                case \"EXPERT\":\n                    window.location.replace(\"http://localhost:3002/dashboard\");\n                    break;\n                case \"CLIENT\":\n                    window.location.replace(\"/?auth=success&role=client&onboarding=complete\");\n                    break;\n                default:\n                    window.location.replace(\"/?auth=success&onboarding=complete\");\n            }\n            return;\n        }\n        // CRITICAL FIX: Always start with role selection for users without completed onboarding\n        // This ensures the role selection page is always shown first\n        if (!session.user.hasCompletedOnboarding) {\n            // Check if user already has a role from previous session\n            if (session.user.role && (session.user.role === \"CLIENT\" || session.user.role === \"EXPERT\")) {\n                setSelectedRole(session.user.role);\n                setCurrentStep(\"data_collection\"); // Skip role selection if role already exists\n            } else if (urlRole && (urlRole === \"CLIENT\" || urlRole === \"EXPERT\")) {\n                setSelectedRole(urlRole);\n                setCurrentStep(\"data_collection\"); // Skip role selection if role in URL\n            } else {\n                // No role found, start with role selection\n                setCurrentStep(\"role_selection\");\n            }\n        }\n    }, [\n        status,\n        session,\n        urlRole,\n        router\n    ]);\n    /**\n   * Handle role selection\n   */ const handleRoleSelection = async (role)=>{\n        try {\n            setIsLoading(true);\n            setSelectedRole(role);\n            // CRITICAL FIX: Update the session with the selected role\n            // This ensures the role persists across page refreshes\n            await update({\n                role: role\n            });\n            console.log(\"✅ Role selected and session updated:\", role);\n            setCurrentStep(\"data_collection\");\n        } catch (error) {\n            console.error(\"❌ Failed to update session with role:\", error);\n            // Continue anyway - role will be saved during onboarding completion\n            setCurrentStep(\"data_collection\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Handle data collection completion\n   */ const handleDataCollectionComplete = (data)=>{\n        setCollectedData(data);\n        setCurrentStep(\"ai_introduction\");\n    };\n    /**\n   * Start AI conversation\n   */ const startAIConversation = async ()=>{\n        var _session_user;\n        if (!selectedRole || !(session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id) || !collectedData) return;\n        try {\n            var _collectedData_location, _collectedData_location1;\n            setIsLoading(true);\n            setError(null);\n            console.log(\"Starting AI conversation with data:\", {\n                userRole: selectedRole,\n                language: \"ar\",\n                sessionType: \"onboarding\",\n                culturalContext: {\n                    location: ((_collectedData_location = collectedData.location) === null || _collectedData_location === void 0 ? void 0 : _collectedData_location.governorate) || \"سوريا\",\n                    dialect: \"general\"\n                },\n                userData: collectedData\n            });\n            const response = await fetch(\"/api/ai/conversation/start\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userRole: selectedRole,\n                    language: \"ar\",\n                    sessionType: \"onboarding\",\n                    culturalContext: {\n                        location: ((_collectedData_location1 = collectedData.location) === null || _collectedData_location1 === void 0 ? void 0 : _collectedData_location1.governorate) || \"سوريا\",\n                        dialect: \"general\"\n                    },\n                    userData: collectedData\n                })\n            });\n            console.log(\"AI conversation start response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"AI conversation start error:\", errorText);\n                throw new Error(\"Failed to start AI conversation: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log(\"AI conversation start response:\", data);\n            if (data.success) {\n                setAiSession(data.data);\n                setCurrentStep(\"chat_conversation\");\n                console.log(\"AI conversation started successfully, moving to chat interface\");\n            } else {\n                throw new Error(data.message || \"Failed to start AI conversation\");\n            }\n        } catch (error) {\n            console.error(\"Failed to start AI conversation:\", error);\n            setError(error.message || \"حدث خطأ في بدء المحادثة الذكية\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Handle conversation completion\n   */ const handleConversationComplete = async (sessionData)=>{\n        try {\n            setIsLoading(true);\n            // Mark onboarding as completed in the database\n            const response = await fetch(\"/api/ai/onboarding/complete\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    sessionId: sessionData.id,\n                    extractedData: sessionData.extractedData\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to complete onboarding\");\n            }\n            setCurrentStep(\"completion\");\n            // Redirect to dashboard after celebration\n            setTimeout(()=>{\n                var _session_user;\n                const userRole = selectedRole || (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role);\n                switch(userRole){\n                    case \"ADMIN\":\n                        window.location.href = \"http://localhost:3001/dashboard\";\n                        break;\n                    case \"EXPERT\":\n                        window.location.href = \"http://localhost:3002/dashboard\";\n                        break;\n                    case \"CLIENT\":\n                        router.push(\"/?auth=success&role=client&onboarding=complete\");\n                        break;\n                    default:\n                        router.push(\"/?auth=success&onboarding=complete\");\n                }\n            }, 3000);\n        } catch (error) {\n            console.error(\"Failed to complete onboarding:\", error);\n            setError(error.message || \"حدث خطأ في إكمال التسجيل\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Handle logout\n   */ const handleLogout = ()=>{\n        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)({\n            callbackUrl: \"/\"\n        });\n    };\n    // Enhanced loading state to prevent flash content\n    if (status === \"loading\" || (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.hasCompletedOnboarding) || isRedirecting) {\n        var _session_user4;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            hideHeaderFooter: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                style: {\n                    background: \"linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2d2d2d 50%, #1a1a1a 75%, #0a0a0a 100%)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-lg font-medium\",\n                            children: (session === null || session === void 0 ? void 0 : (_session_user4 = session.user) === null || _session_user4 === void 0 ? void 0 : _session_user4.hasCompletedOnboarding) ? \"جاري التوجيه...\" : \"جاري التحميل...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n            lineNumber: 270,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-md mx-auto p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-500 text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"حدث خطأ\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"w-full px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n                                    children: \"إعادة المحاولة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleLogout,\n                                    className: \"w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"تسجيل الخروج\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_seo__WEBPACK_IMPORTED_MODULE_5__.NextSeo, {\n                title: \"التسجيل الذكي - فريلا سوريا\",\n                description: \"أكمل تسجيلك في منصة فريلا سوريا باستخدام المساعد الذكي\",\n                noindex: true,\n                nofollow: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                hideHeaderFooter: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen relative overflow-hidden\",\n                    style: {\n                        background: \"linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2d2d2d 50%, #1a1a1a 75%, #0a0a0a 100%)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-30\",\n                            style: {\n                                backgroundImage: \"\\n                radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),\\n                radial-gradient(circle at 75% 75%, rgba(255, 215, 0, 0.08) 0%, transparent 50%),\\n                radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.05) 0%, transparent 50%)\\n              \"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_OnboardingProgress__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            currentStep: currentStep,\n                            selectedRole: selectedRole,\n                            completionRate: (aiSession === null || aiSession === void 0 ? void 0 : aiSession.completionRate) || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-8 relative z-10\",\n                            children: [\n                                currentStep === \"role_selection\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_RoleSelection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    onRoleSelect: handleRoleSelection,\n                                    selectedRole: selectedRole,\n                                    isLoading: isLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentStep === \"data_collection\" && selectedRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_DataCollectionForm__WEBPACK_IMPORTED_MODULE_8__.DataCollectionForm, {\n                                    onSubmit: handleDataCollectionComplete,\n                                    onBack: ()=>{\n                                        // Go back to role selection and preserve the selected role\n                                        setCurrentStep(\"role_selection\");\n                                    },\n                                    isLoading: isLoading,\n                                    selectedRole: selectedRole,\n                                    initialData: {\n                                        firstName: (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : (_session_user_name = _session_user1.name) === null || _session_user_name === void 0 ? void 0 : _session_user_name.split(\" \")[0]) || \"\",\n                                        lastName: (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : (_session_user_name1 = _session_user2.name) === null || _session_user_name1 === void 0 ? void 0 : _session_user_name1.split(\" \").slice(1).join(\" \")) || \"\",\n                                        email: (session === null || session === void 0 ? void 0 : (_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : _session_user3.email) || \"\",\n                                        // Preserve previously collected data if available\n                                        ...collectedData\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentStep === \"ai_introduction\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_AIIntroduction__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    selectedRole: selectedRole,\n                                    onStartConversation: startAIConversation,\n                                    isLoading: isLoading,\n                                    onBack: ()=>{\n                                        // Go back to data collection to allow editing\n                                        setCurrentStep(\"data_collection\");\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentStep === \"chat_conversation\" && aiSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_ChatInterface__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    session: aiSession,\n                                    onComplete: handleConversationComplete,\n                                    isLoading: isLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentStep === \"completion\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_CompletionCelebration__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    selectedRole: selectedRole,\n                                    extractedData: aiSession === null || aiSession === void 0 ? void 0 : aiSession.extractedData\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AIOnboardingPage, \"S8jWl/Q77RRPShhCuG4lOQTVSiU=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession\n    ];\n});\n_c = AIOnboardingPage;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AIOnboardingPage);\nvar _c;\n$RefreshReg$(_c, \"AIOnboardingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/ai-onboarding.tsx\n"));

/***/ })

});